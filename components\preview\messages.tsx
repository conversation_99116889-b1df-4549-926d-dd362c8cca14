import { ChatRequestOptions, Message, CreateMessage } from 'ai';
import { PreviewMessage, ThinkingMessage } from './message';
import { useScrollToBottom } from '@/lib/hooks/use-scroll-to-bottom';
import { Overview } from '@/components/preview/overview';
import { SuggestedActions } from '@/components/preview/suggested-actions';
import { ServiceType } from '@/components/service-selector';
// Preview 모드에서는 UIBlock 사용하지 않음
// import { UIBlock } from '@/components/block';
import { Dispatch, memo, SetStateAction } from 'react';
// Vote 타입을 로컬에서 정의 (DB 의존성 제거)
type Vote = {
  id: string;
  messageId: string;
  chatId: string;
  isUpvoted: boolean;
};
import equal from 'fast-deep-equal';

interface MessagesProps {
  chatId: string;
  isLoading: boolean;
  votes: Array<Vote> | undefined;
  messages: Array<Message>;
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[]),
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  append?: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  selectedModelId?: string;
  selectedService?: ServiceType;
  error: Error | undefined | null; // error prop 추가
  status: 'submitted' | 'streaming' | 'ready' | 'error';
}

function PureMessages({
  chatId,
  isLoading,
  status,
  votes,
  messages,
  setMessages,
  reload,
  append,
  error,
  isReadonly,
  selectedModelId,
  selectedService,
}: MessagesProps) {
  const [messagesContainerRef, messagesEndRef] =
    useScrollToBottom<HTMLDivElement>();

  return (
    <div
      ref={messagesContainerRef}
      className="flex flex-col min-w-0 gap-6 flex-1 overflow-y-auto px-4 pt-4 styled-scrollbar"
    >
      {messages.length === 0 && (
        <div className="mx-auto max-w-2xl px-4">
          <div className="flex flex-col gap-4">
            <Overview 
              selectedModelId={selectedModelId} 
              selectedService={selectedService}
            />
            {!isReadonly && append && (
              <SuggestedActions
                chatId={chatId}
                append={append}
                selectedModelId={selectedModelId}
                selectedService={selectedService}
              />
            )}
          </div>
        </div>
      )}

      {messages.map((message, index) => (
        <PreviewMessage
          key={message.id}
          chatId={chatId}
          message={message}
          isLoading={isLoading && messages.length - 1 === index}
          vote={
            votes
              ? votes.find((vote) => vote.messageId === message.id)
              : undefined
          }
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
        />
      ))}

      {(status === 'submitted') &&
        <ThinkingMessage />}

      {/* Status and Error Messages */}
      <div className="px-4 py-2 text-center text-sm">
        {status === 'error' && error && (
          <p className="text-red-500">
            오류가 발생했습니다.
            지속적으로 발생되는 경우 새 대화를 시도해주세요.
            {error.message}
          </p>
        )}

      </div>

      <div
        ref={messagesEndRef}
        className="shrink-0 min-w-[24px] min-h-[24px]"
      />
    </div>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.isLoading && nextProps.isLoading) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;
  if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;
  if (prevProps.selectedService !== nextProps.selectedService) return false;

  return true;
});
