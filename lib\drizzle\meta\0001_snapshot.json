{"id": "43b89b2d-8a93-4f18-8ec8-5c68d36aec1d", "prevId": "724dfb92-6a76-4062-9556-c5ee9ff7a7cc", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token_expires_in": {"name": "refresh_token_expires_in", "type": "integer", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}, "oauth_token_secret": {"name": "oauth_token_secret", "type": "text", "primaryKey": false, "notNull": false}, "oauth_token": {"name": "oauth_token", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"accounts_userId_index": {"name": "accounts_userId_index", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"accounts_provider_providerAccountId_pk": {"name": "accounts_provider_providerAccountId_pk", "columns": ["provider", "providerAccountId"]}}, "uniqueConstraints": {}}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "messages": {"name": "messages", "type": "json", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"chat_userId_users_id_fk": {"name": "chat_userId_users_id_fk", "tableFrom": "chat", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"sessions_userId_index": {"name": "sessions_userId_index", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.systemConfig": {"name": "systemConfig", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": true, "notNull": true}, "id": {"name": "id", "type": "text", "primaryKey": false, "notNull": false}, "systemMessage": {"name": "systemMessage", "type": "text", "primaryKey": false, "notNull": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": false}, "chatbotId": {"name": "chatbotId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"systemConfig_userId_users_id_fk": {"name": "systemConfig_userId_users_id_fk", "tableFrom": "systemConfig", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "gh_username": {"name": "gh_username", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "public.verificationTokens": {"name": "verificationTokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationTokens_identifier_token_pk": {"name": "verificationTokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {"verificationTokens_token_unique": {"name": "verificationTokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}