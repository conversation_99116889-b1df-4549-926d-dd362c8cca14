#!/bin/bash

# 폐쇄망 설치를 위한 이미지 준비 스크립트
echo "=== 폐쇄망 설치용 이미지 준비 스크립트 ==="

# 스크립트 디렉토리 확인
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "스크립트 디렉토리: $SCRIPT_DIR"
echo "프로젝트 루트: $PROJECT_ROOT"

# package.json 파일 확인
if [ ! -f "$PROJECT_ROOT/package.json" ]; then
    echo "❌ package.json 파일을 찾을 수 없습니다."
    echo "현재 위치: $(pwd)"
    echo "프로젝트 루트: $PROJECT_ROOT"
    echo "사용법: cd /path/to/lx-chatbot && ./install/prepare-offline.sh"
    exit 1
fi

echo "✓ 프로젝트 루트 확인 완료: $PROJECT_ROOT"

# 1. 애플리케이션 빌드 (프로젝트 루트에서)
echo "1. 애플리케이션 빌드 중..."
cd "$PROJECT_ROOT"
docker build -t lx-chatbot/frontend:latest -f Dockerfile .
if [ $? -ne 0 ]; then
    echo "❌ 빌드 실패"
    exit 1
fi
cd "$SCRIPT_DIR"

# 2. 이미지 저장
echo "2. Docker 이미지 저장 중..."

# 메인 애플리케이션 이미지 저장
docker save lx-chatbot/frontend:latest -o lx-chatbot-frontend.tar
echo "✓ lx-chatbot-frontend.tar 저장 완료"

# 베이스 이미지 저장
docker save node:20-alpine -o node-20-alpine.tar
echo "✓ node-20-alpine.tar 저장 완료"

# 3. 파일 크기 확인
echo "3. 저장된 파일 정보:"
ls -lh *.tar

# 4. 필요한 파일들 복사
echo "4. 설치 파일들 준비 중..."
chmod +x install-offline.sh

echo ""
echo "=== 준비 완료 ==="
echo "폐쇄망으로 복사할 파일들:"
echo "- lx-chatbot-frontend.tar"
echo "- node-20-alpine.tar"
echo "- docker-compose.yml"
