#!/bin/bash

# 폐쇄망 환경에서 LX Chatbot 설치 스크립트 (Linux/macOS)
echo "=== LX Chatbot 폐쇄망 설치 스크립트 ==="

# 1. Docker 설치 확인
if ! command -v docker &> /dev/null; then
    echo "❌ Docker가 설치되어 있지 않습니다."
    echo "Docker를 먼저 설치해주세요: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose가 설치되어 있지 않습니다."
    echo "Docker Compose를 먼저 설치해주세요."
    exit 1
fi

echo "✓ Docker 및 Docker Compose 확인 완료"

# 2. 이미지 로드
echo "2. Docker 이미지 로드 중..."

if [ -f "lx-chatbot-frontend.tar" ]; then
    docker load -i lx-chatbot-frontend.tar
    echo "✓ lx-chatbot-frontend 이미지 로드 완료"
else
    echo "❌ lx-chatbot-frontend.tar 파일을 찾을 수 없습니다."
    exit 1
fi

if [ -f "node-20-alpine.tar" ]; then
    docker load -i node-20-alpine.tar
    echo "✓ node-20-alpine 이미지 로드 완료"
else
    echo "⚠️  node-20-alpine.tar 파일을 찾을 수 없습니다. (이미 존재할 수 있음)"
fi

# 3. 환경변수 파일 확인
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✓ .env.example을 .env로 복사했습니다."
        echo "⚠️  .env 파일을 편집하여 환경변수를 설정해주세요."
    else
        echo "❌ .env 파일이 없습니다. 환경변수를 설정해주세요."
        cat > .env << EOF
# LX Chatbot 환경변수 설정
DIFY_APP_KEY=your_dify_app_key_here
DIFY_URL=http://your-dify-server:port
VLLM_BASE_URL=http://your-vllm-server:port/v1
VLLM_API_KEY=your_vllm_api_key_here
EOF
        echo "✓ 기본 .env 파일을 생성했습니다. 값을 수정해주세요."
    fi
fi

# 4. 컨테이너 실행
echo "4. 컨테이너 실행 중..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d
else
    docker compose up -d
fi

# 5. 상태 확인
echo "5. 설치 상태 확인 중..."
sleep 5

if command -v docker-compose &> /dev/null; then
    docker-compose ps
else
    docker compose ps
fi

echo ""
echo "=== 설치 완료 ==="
echo "LX Chatbot이 http://localhost:3002 에서 실행 중입니다."
echo ""
echo "유용한 명령어:"
echo "- 로그 확인: docker-compose logs -f"
echo "- 컨테이너 중지: docker-compose down"
echo "- 컨테이너 재시작: docker-compose restart"
