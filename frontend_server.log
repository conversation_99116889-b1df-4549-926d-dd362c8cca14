
> lx-chatbot@0.1.0 dev /Users/<USER>/Documents/wavus/0.GeOnAI/better-gpt/chatbot/0.Project/3.AI_business/LX/integration/v2_backend/lx-chatbot-front
> next dev --turbopack --port 3000

   ▲ Next.js 15.3.3 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://*********:3000
   - Environments: .env

 ✓ Starting...
 ✓ Compiled middleware in 65ms
 ✓ Ready in 793ms
[MIDDLEWARE] Preview mode - processing path: /
[MIDDLEWARE] Root path, redirecting to /preview
[MIDDLEWARE] Preview mode - processing path: /preview
[MIDD<PERSON>WARE] Preview mode - allowing all requests
 ○ Compiling /preview ...
 ✓ Compiled /preview in 1467ms
 GET /preview 200 in 1832ms
 ✓ Compiled /manifest.webmanifest in 183ms
 GET /manifest.webmanifest 200 in 214ms
 ✓ Compiled /api/dev-chat in 276ms
 POST /api/dev-chat 200 in 15133ms
 POST /api/dev-chat 200 in 17174ms
[MID<PERSON><PERSON><PERSON>RE] Preview mode - processing path: /
[MID<PERSON><PERSON><PERSON><PERSON>] Root path, redirecting to /preview
[MID<PERSON><PERSON><PERSON><PERSON>] Preview mode - processing path: /preview
[MIDD<PERSON><PERSON>RE] Preview mode - allowing all requests
 GET /preview 200 in 85ms
 GET /manifest.webmanifest 200 in 16ms
 POST /api/dev-chat 200 in 15689ms
