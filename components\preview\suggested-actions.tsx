'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { memo } from 'react';
import { ServiceType } from '@/components/service-selector';

interface SuggestedActionsProps {
  chatId: string;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  selectedModelId?: string;
  selectedService?: ServiceType;
}

// 서비스별 제안 액션 정의
const serviceSuggestedActions = {
  geon: [
    {
      title: '공유재산 관리',
      label: '공유재산심의회 구성에 대해 알려줘',
      action: '공유재산심의회 구성에 대해 알려줘',
    },
    {
      title: '업무 절차',
      label: '공유재산 용도 폐지는 어떻게 하는거야?',
      action: '공유재산 용도 폐지는 어떻게 하는거야?',
    },
    {
      title: '법령 및 규정',
      label: '공유재산 정의의 근거에 대해 자세히 알려줘',
      action: '공유재산 정의의 근거에 대해 자세히 알려줘',
    },
    {
      title: '공유재산 일반',
      label: '공유재산 범위 좀 알려줘',
      action: '공유재산 범위 좀 알려줘',
    },
  ],
  manual: [
    {
      title: '수체납 관리',
      label: '수체납 관리 화면은 어디에 있어? 사용방법을 알려줘',
      action: '수체납 관리 화면은 어디에 있어? 사용방법을 알려줘',
    },
    {
      title: '메인화면 기능',
      label: '메인화면에는 어떤 기능이 포함되어 있어?',
      action: '메인화면에는 어떤 기능이 포함되어 있어?',
    },
    {
      title: '현황지도',
      label: '메인화면의 현황지도 버튼은 어디에 있어?',
      action: '메인화면의 현황지도 버튼은 어디에 있어?',
    },
    {
      title: '협의 요청',
      label: '위탁기관에서 협의 요청하려면?',
      action: '위탁기관에서 협의 요청하려면?',
    },
  ]
};

function PureSuggestedActions({ chatId, append, selectedModelId, selectedService = 'geon' }: SuggestedActionsProps) {
  // 선택된 서비스에 따른 제안 액션 사용
  const suggestedActions = serviceSuggestedActions[selectedService];

  return (
    <div className="grid sm:grid-cols-2 gap-3 sm:gap-4 w-full">
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${selectedService}-${suggestedAction.title}-${index}`}
          className="block"
        >
          <Button
            variant="outline"
            onClick={async () => {
              // window.history.replaceState({}, '', `/chat/${chatId}`);

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="group text-left border-2 rounded-xl px-4 py-4 sm:px-5 sm:py-4 text-sm flex-1 gap-2 sm:flex-col w-full h-auto justify-start items-start bg-white hover:bg-blue-700 border-gray-200 hover:border-blue-700 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 transition-all duration-300 shadow-sm hover:shadow-md backdrop-blur-sm hover:scale-[1.02] active:scale-[0.98] active:bg-blue-800 min-h-[80px] sm:min-h-[100px]"
          >
            <span className="font-semibold text-gray-800 group-hover:text-white group-active:text-white transition-colors duration-200 text-base sm:text-sm">{suggestedAction.title}</span>
            <span className="text-gray-600 group-hover:text-gray-100 group-active:text-gray-100 transition-colors duration-200 text-sm sm:text-xs leading-relaxed">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, (prevProps, nextProps) => {
  // selectedService가 변경되면 리렌더링
  if (prevProps.selectedService !== nextProps.selectedService) return false;
  if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;
  if (prevProps.chatId !== nextProps.chatId) return false;
  
  return true;
});
