import {NextAuthConfig} from "next-auth";

export const authConfig = {
	pages: {
		signIn: "/login",
		// verifyRequest: `/login`,
		// error: "/login", // Error code passed in query string as ?error=
		newUser: "/",
	},
	providers: [
		// added later in auth.ts since it requires bcrypt which is only compatible with Node.js
		// while this file is also used in non-Node.js environments
	],
	callbacks: {
		authorized({ auth, request: { nextUrl } }) {
			const isOnChat = nextUrl.pathname === "/";
			const isOnLogin = nextUrl.pathname.startsWith("/login");
	  
			// 로그인 페이지 접근은 항상 가능
			if (isOnLogin) {
			  return true;
			}

			// 루트(채팅) 페이지는 로그인 없이 접근 가능(Preview 모드)
			if (isOnChat) {
			  return true;
			}
	  
			return true;
		  },
	},
} satisfies NextAuthConfig;
