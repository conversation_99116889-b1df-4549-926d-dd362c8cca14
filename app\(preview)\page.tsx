import { cookies } from 'next/headers';

import { Chat } from '@/components/preview/chat';
import { DEFAULT_MODEL_NAME, models } from '@/lib/ai/dev-models';
import { generateUUID } from '@/lib/utils';
import { DataStreamHandler } from '@/components/data-stream-handler';
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "업무지원(챗봇)",
  description: "AI 기반 지능형 어시스턴트로 다양한 작업을 수행하세요. 자연어 처리와 고급 AI 기능을 통해 효율적인 업무 지원을 제공합니다.",
  keywords: ["AI 어시스턴트", "챗봇", "자연어 처리", "AI", "지능형 어시스턴트", "업무 자동화"],
  openGraph: {
    title: "업무지원(챗봇)",
    description: "AI 기반 지능형 어시스턴트로 다양한 작업을 수행하세요. 자연어 처리와 고급 AI 기능을 통해 효율적인 업무 지원을 제공합니다.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "업무지원(챗봇)",
    description: "AI 기반 지능형 어시스턴트로 다양한 작업을 수행하세요. 자연어 처리와 고급 AI 기능을 통해 효율적인 업무 지원을 제공합니다.",
  },
};

export default async function Page() {
  const id = generateUUID();

  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get('dev-model-id')?.value;

  const selectedModelId =
    models.find((model) => model.id === modelIdFromCookie)?.id ||
    DEFAULT_MODEL_NAME;

  return (
    <>
      <Chat
        key={id}
        id={id}
        initialMessages={[]}
        selectedModelId={selectedModelId}
        selectedVisibilityType="private"
        isReadonly={false}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
