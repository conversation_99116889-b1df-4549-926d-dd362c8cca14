FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat python3 make g++
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
# Copy .npmrc if it exists (for private registry configuration)
COPY .npmrc* ./
RUN \
  echo "Before: corepack version => $(corepack --version || echo 'not installed')" && \
  npm install -g corepack@latest && \
  echo "After : corepack version => $(corepack --version)" && \
  corepack enable && \
  pnpm --version && \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi


# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .


ENV NODE_ENV=production

RUN \
  echo "Before: corepack version => $(corepack --version || echo 'not installed')" && \
  npm install -g corepack@latest && \
  echo "After : corepack version => $(corepack --version)" && \
  corepack enable && \
  pnpm --version && \
  if [ -f yarn.lock ]; then yarn run build:ci; \
  elif [ -f package-lock.json ]; then npm run build:ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app
# 빌드 시 주입되는 base path (예: /ai). 빈 문자열이면 루트.
ARG NEXT_BASE_PATH=""
ARG NEXT_ASSET_PREFIX=""

ENV NEXT_BASE_PATH=${NEXT_BASE_PATH}
ENV NEXT_ASSET_PREFIX=${NEXT_ASSET_PREFIX}
ENV NODE_ENV=production
ENV NEXT_PUBLIC_BASE_URL=http://localhost:3000


RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT=3000

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["sh", "-c", "HOSTNAME=0.0.0.0 node server.js"]
