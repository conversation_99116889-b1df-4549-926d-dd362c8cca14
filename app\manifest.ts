import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: '공유재산 관리 어시스턴트',
    short_name: 'LX Assistant',
    description: 'AI 기반 공유재산 관리 및 상담 서비스 - 전문적인 공유재산 관리 어시스턴트',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#1e40af',
    icons: [
      {
        src: '/favicon.ico',
        sizes: 'any',
        type: 'image/x-icon',
      },
      {
        src: '/images/main-logo.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/images/main-logo.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
    categories: ['productivity', 'business', 'finance', 'government'],
    lang: 'ko',
    orientation: 'portrait-primary',
  }
}
