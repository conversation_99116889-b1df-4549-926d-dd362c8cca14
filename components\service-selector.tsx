'use client';

import * as React from 'react';
import { Check, ChevronDown } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

export type ServiceType = 'geon' | 'manual';

export interface ServiceOption {
  id: ServiceType;
  name: string;
  description: string;
  pipeline: string;
}

export const serviceOptions: ServiceOption[] = [
  {
    id: 'geon',
    name: 'LX 공유재산 위탁관리 업무 도우미',
    description: 'Iteration-based Pipeline',
    pipeline: 'iteration_based_rag'
  },
  {
    id: 'manual',
    name: 'LX 재산관리 통합체계 사용 도우미',
    description: 'Manual Pipeline',
    pipeline: 'manual_rag'
  }
];

interface ServiceSelectorProps {
  selectedServiceId: ServiceType;
  onServiceChange: (serviceId: ServiceType) => void;
  className?: string;
}

export function ServiceSelector({
  selectedServiceId,
  onServiceChange,
  className
}: ServiceSelectorProps) {
  const selectedService = serviceOptions.find(service => service.id === selectedServiceId);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'flex justify-between items-center w-full max-w-xs',
            className
          )}
        >
          <div className="flex flex-col items-start text-left">
            <span className="text-sm font-medium truncate">
              {selectedService?.name || '서비스 선택'}
            </span>
            <span className="text-xs text-muted-foreground truncate">
              {selectedService?.description}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-80">
        {serviceOptions.map((service) => (
          <DropdownMenuItem
            key={service.id}
            onSelect={() => onServiceChange(service.id)}
            className="flex items-center gap-3 p-3"
          >
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <span className="font-medium">{service.name}</span>
                {selectedServiceId === service.id && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </div>
              <div className="text-sm text-muted-foreground mt-1">
                {service.description}
              </div>
              <div className="text-xs text-muted-foreground mt-1 font-mono">
                Pipeline: {service.pipeline}
              </div>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}